/**
 * 用户表数据库操作工具
 * 基于微信小程序云开发数据库 API
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/reference-sdk-api/Cloud.database.html
 */

// 获取数据库引用
const db = wx.cloud.database();
const _ = db.command;

// 引入实时日志工具
const realtimeLog = require('./realtime-log.js');

// 用户表集合名称
const USER_COLLECTION = 'User';

/**
 * 根据 owner 字段查询用户信息
 * @param {string} owner - 用户的 owner 字段值
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserByOwner = async (owner) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始查询用户信息，owner:', owner);

    // 执行数据库查询
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .get();

    console.log('用户查询结果:', result);

    // 检查查询结果
    if (result.data && result.data.length > 0) {
      // 返回第一个匹配的用户（通常 owner 应该是唯一的）
      return {
        success: true,
        data: result.data[0],
        message: '用户信息查询成功'
      };
    } else {
      // 未找到用户
      return {
        success: false,
        data: null,
        message: '未找到对应的用户信息'
      };
    }

  } catch (error) {
    console.error('查询用户信息失败:', error);
    
    // 返回错误信息
    return {
      success: false,
      data: null,
      message: error.message || '查询用户信息时发生错误',
      error: error
    };
  }
};

/**
 * 创建新用户
 * @param {Object} userData - 用户数据对象
 * @param {string} userData.owner - 所有人字段
 * @param {string} userData.nick_name - 昵称
 * @param {string} userData.avatar_url - 头像URL
 * @param {Array} userData.my_calendar - 我的日历ID数组
 * @param {Array} userData.collected_calendar - 收藏日历ID数组
 * @returns {Promise} 返回创建结果的 Promise
 */
const createUser = async (userData) => {
  try {
    // 参数验证
    if (!userData || typeof userData !== 'object') {
      throw new Error('用户数据必须是对象类型');
    }

    if (!userData.owner || typeof userData.owner !== 'string') {
      throw new Error('owner 字段必须是非空字符串');
    }

    console.log('开始创建用户:', userData);

    // 构建用户数据
    const userDoc = {
      owner: userData.owner,
      nick_name: userData.nick_name || '',
      avatar_url: userData.avatar_url || '',
      my_calendar: userData.my_calendar || [],
      collected_calendar: userData.collected_calendar || []
    };

    // 执行数据库插入
    const result = await db.collection(USER_COLLECTION).add({
      data: userDoc
    });

    console.log('用户创建结果:', result);

    return {
      success: true,
      data: {
        _id: result._id,
        ...userDoc
      },
      message: '用户创建成功'
    };

  } catch (error) {
    console.error('创建用户失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '创建用户时发生错误',
      error: error
    };
  }
};

/**
 * 更新用户信息
 * @param {string} owner - 用户的 owner 字段值
 * @param {Object} updateData - 要更新的数据
 * @returns {Promise} 返回更新结果的 Promise
 */
const updateUserByOwner = async (owner, updateData) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    if (!updateData || typeof updateData !== 'object') {
      throw new Error('更新数据必须是对象类型');
    }

    console.log('开始更新用户信息，owner:', owner, '更新数据:', updateData);

    // 过滤掉不允许更新的系统字段
    const allowedFields = ['nick_name', 'avatar_url', 'my_calendar', 'collected_calendar'];
    const filteredData = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    // 执行数据库更新
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: filteredData
      });

    console.log('用户更新结果:', result);

    return {
      success: true,
      data: result,
      message: '用户信息更新成功'
    };

  } catch (error) {
    console.error('更新用户信息失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '更新用户信息时发生错误',
      error: error
    };
  }
};

/**
 * 添加日历到用户的"我的日历"列表
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} calendarId - 要添加的日历ID
 * @returns {Promise} 返回操作结果的 Promise
 */
const addToMyCalendar = async (owner, calendarId) => {
  try {
    if (!owner || !calendarId) {
      throw new Error('owner 和 calendarId 参数都是必需的');
    }

    console.log('添加日历到我的日历列表，owner:', owner, 'calendarId:', calendarId);

    // 使用数组操作符添加元素（避免重复）
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: {
          my_calendar: db.command.addToSet(calendarId)
        }
      });

    return {
      success: true,
      data: result,
      message: '日历添加成功'
    };

  } catch (error) {
    console.error('添加日历失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '添加日历时发生错误',
      error: error
    };
  }
};

/**
 * 添加日历到用户的"收藏日历"列表
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} calendarId - 要收藏的日历ID
 * @returns {Promise} 返回操作结果的 Promise
 */
const addToCollectedCalendar = async (owner, calendarId) => {
  try {
    if (!owner || !calendarId) {
      throw new Error('owner 和 calendarId 参数都是必需的');
    }

    console.log('添加日历到收藏列表，owner:', owner, 'calendarId:', calendarId);

    // 使用数组操作符添加元素（避免重复）
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: {
          collected_calendar: db.command.addToSet(calendarId)
        }
      });

    return {
      success: true,
      data: result,
      message: '日历收藏成功'
    };

  } catch (error) {
    console.error('收藏日历失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '收藏日历时发生错误',
      error: error
    };
  }
};

/**
 * 从用户的"收藏日历"列表中移除日历
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} calendarId - 要移除的日历ID
 * @returns {Promise} 返回操作结果的 Promise
 */
const removeFromCollectedCalendar = async (owner, calendarId) => {
  try {
    if (!owner || !calendarId) {
      throw new Error('owner 和 calendarId 参数都是必需的');
    }

    console.log('从收藏列表移除日历，owner:', owner, 'calendarId:', calendarId);

    // 使用数组操作符移除元素
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: {
          collected_calendar: db.command.pull(calendarId)
        }
      });

    return {
      success: true,
      data: result,
      message: '日历取消收藏成功'
    };

  } catch (error) {
    console.error('取消收藏日历失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '取消收藏日历时发生错误',
      error: error
    };
  }
};

/**
 * 批量查询用户信息
 * @param {Array<string>} ownerList - 用户owner字段值数组
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUsersByOwnerList = async (ownerList) => {
  try {
    // 参数验证
    if (!Array.isArray(ownerList) || ownerList.length === 0) {
      throw new Error('ownerList 参数必须是非空数组');
    }

    console.log('开始批量查询用户信息，ownerList:', ownerList);

    // 执行数据库查询
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: db.command.in(ownerList)
      })
      .get();

    console.log('批量用户查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 个用户信息`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('批量查询用户信息失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '批量查询用户信息时发生错误',
      error: error
    };
  }
};

/**
 * 上传头像到云存储
 * @param {string} tempFilePath - 临时文件路径
 * @param {string} owner - 用户的 owner 字段值
 * @returns {Promise} 返回上传结果的 Promise
 */
const uploadAvatarToCloud = async (tempFilePath, owner) => {
  const startTime = Date.now();

  try {
    // 记录开始上传
    realtimeLog.logInfo('头像上传开始', {
      tempFilePath,
      owner,
      operation: 'uploadAvatarToCloud'
    });

    if (!tempFilePath || !owner) {
      const errorMsg = '缺少必要参数：tempFilePath 或 owner';
      realtimeLog.logError('头像上传参数错误', new Error(errorMsg), {
        tempFilePath,
        owner,
        operation: 'uploadAvatarToCloud'
      });
      throw new Error(errorMsg);
    }

    // 生成唯一的文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const fileName = `avatars/${owner}_${timestamp}_${randomStr}.jpg`;

    console.log('开始上传头像到云存储:', { tempFilePath, fileName });

    realtimeLog.logInfo('头像文件名生成', {
      fileName,
      owner,
      timestamp,
      randomStr,
      operation: 'uploadAvatarToCloud'
    });

    // 上传到云存储
    const uploadResult = await wx.cloud.uploadFile({
      cloudPath: fileName,
      filePath: tempFilePath
    });

    const uploadDuration = Date.now() - startTime;

    if (uploadResult.fileID) {
      console.log('头像上传成功:', uploadResult.fileID);

      // 记录上传成功
      realtimeLog.logInfo('头像上传成功', {
        fileID: uploadResult.fileID,
        cloudPath: fileName,
        owner,
        uploadDuration: `${uploadDuration}ms`,
        originalPath: tempFilePath,
        operation: 'uploadAvatarToCloud'
      });

      return {
        success: true,
        fileID: uploadResult.fileID,
        cloudPath: fileName
      };
    } else {
      const errorMsg = '上传失败，未获得文件ID';
      realtimeLog.logError('头像上传失败', new Error(errorMsg), {
        uploadResult,
        fileName,
        owner,
        uploadDuration: `${uploadDuration}ms`,
        operation: 'uploadAvatarToCloud'
      });
      throw new Error(errorMsg);
    }
  } catch (error) {
    const uploadDuration = Date.now() - startTime;
    console.error('上传头像失败:', error);

    // 记录上传失败
    realtimeLog.logError('头像上传异常', error, {
      tempFilePath,
      owner,
      uploadDuration: `${uploadDuration}ms`,
      errorMessage: error.message,
      operation: 'uploadAvatarToCloud'
    });

    return {
      success: false,
      message: error.message || '上传头像失败',
      error: error
    };
  }
};

/**
 * 创建或更新用户信息（用于预约时的用户信息收集）
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} nickName - 用户昵称
 * @param {string} avatarUrl - 用户头像URL
 * @returns {Promise} 返回操作结果的 Promise
 */
const createOrUpdateUserProfile = async (owner, nickName, avatarUrl) => {
  const startTime = Date.now();

  try {
    // 记录开始处理用户资料
    realtimeLog.logInfo('用户资料处理开始', {
      owner,
      nickName,
      avatarUrl,
      operation: 'createOrUpdateUserProfile'
    });

    // 参数验证
    if (!owner || typeof owner !== 'string') {
      const errorMsg = 'owner 参数必须是非空字符串';
      realtimeLog.logError('用户资料参数错误', new Error(errorMsg), {
        owner,
        nickName,
        avatarUrl,
        operation: 'createOrUpdateUserProfile'
      });
      throw new Error(errorMsg);
    }

    console.log('创建或更新用户资料，owner:', owner, 'nickName:', nickName, 'avatarUrl:', avatarUrl);

    let finalAvatarUrl = avatarUrl;
    let avatarProcessingResult = null;

    // 检查是否是临时文件路径，如果是则上传到云存储
    const isTempAvatar = avatarUrl && (avatarUrl.startsWith('http://tmp/') || avatarUrl.includes('wxfile://') || avatarUrl.startsWith('wxfile://'));

    if (isTempAvatar) {
      console.log('检测到临时头像路径，开始上传到云存储:', avatarUrl);

      // 记录检测到临时头像
      realtimeLog.logInfo('检测到临时头像', {
        originalAvatarUrl: avatarUrl,
        owner,
        operation: 'createOrUpdateUserProfile'
      });

      const uploadResult = await uploadAvatarToCloud(avatarUrl, owner);
      avatarProcessingResult = uploadResult;

      if (uploadResult.success) {
        finalAvatarUrl = uploadResult.fileID;
        console.log('头像上传成功，新的URL:', finalAvatarUrl);

        // 记录头像处理成功
        realtimeLog.logInfo('临时头像处理成功', {
          originalUrl: avatarUrl,
          finalUrl: finalAvatarUrl,
          cloudPath: uploadResult.cloudPath,
          owner,
          operation: 'createOrUpdateUserProfile'
        });
      } else {
        console.warn('头像上传失败，使用默认头像:', uploadResult.message);
        // 使用默认头像
        finalAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';

        // 记录头像处理失败
        realtimeLog.logError('临时头像处理失败', new Error(uploadResult.message), {
          originalUrl: avatarUrl,
          fallbackUrl: finalAvatarUrl,
          uploadError: uploadResult.error,
          owner,
          operation: 'createOrUpdateUserProfile'
        });
      }
    } else if (avatarUrl) {
      // 记录使用现有头像
      realtimeLog.logInfo('使用现有头像URL', {
        avatarUrl,
        owner,
        operation: 'createOrUpdateUserProfile'
      });
    }

    // 先查询用户是否存在
    realtimeLog.logInfo('查询用户是否存在', {
      owner,
      operation: 'createOrUpdateUserProfile'
    });

    const existingUser = await readUserByOwner(owner);
    const processingDuration = Date.now() - startTime;

    if (existingUser.success && existingUser.data) {
      // 用户存在，更新信息
      realtimeLog.logInfo('用户已存在，准备更新', {
        owner,
        existingNickName: existingUser.data.nick_name,
        existingAvatarUrl: existingUser.data.avatar_url,
        newNickName: nickName,
        newAvatarUrl: finalAvatarUrl,
        operation: 'createOrUpdateUserProfile'
      });

      const updateData = {};
      if (nickName) updateData.nick_name = nickName;
      if (finalAvatarUrl) updateData.avatar_url = finalAvatarUrl;

      if (Object.keys(updateData).length > 0) {
        const updateResult = await updateUserByOwner(owner, updateData);

        // 记录更新结果
        realtimeLog.logDbOperation('updateUserProfile', 'user', {
          owner,
          updateData,
          avatarProcessing: avatarProcessingResult,
          processingDuration: `${processingDuration}ms`
        }, updateResult.success, updateResult);

        return {
          ...updateResult,
          avatarUrl: finalAvatarUrl // 返回最终的头像URL
        };
      } else {
        // 记录无需更新
        realtimeLog.logInfo('用户信息无需更新', {
          owner,
          finalAvatarUrl,
          processingDuration: `${processingDuration}ms`,
          operation: 'createOrUpdateUserProfile'
        });

        return {
          success: true,
          data: existingUser.data,
          message: '用户信息无需更新',
          avatarUrl: finalAvatarUrl
        };
      }
    } else {
      // 用户不存在，创建新用户
      const userData = {
        owner: owner,
        nick_name: nickName || '微信用户',
        avatar_url: finalAvatarUrl || '',
        my_calendar: [],
        collected_calendar: []
      };

      realtimeLog.logInfo('用户不存在，准备创建', {
        owner,
        userData,
        avatarProcessing: avatarProcessingResult,
        operation: 'createOrUpdateUserProfile'
      });

      const createResult = await createUser(userData);

      // 记录创建结果
      realtimeLog.logDbOperation('createUserProfile', 'user', {
        owner,
        userData,
        avatarProcessing: avatarProcessingResult,
        processingDuration: `${processingDuration}ms`
      }, createResult.success, createResult);

      return {
        ...createResult,
        avatarUrl: finalAvatarUrl // 返回最终的头像URL
      };
    }

  } catch (error) {
    const processingDuration = Date.now() - startTime;
    console.error('创建或更新用户资料失败:', error);

    // 记录处理失败
    realtimeLog.logError('用户资料处理失败', error, {
      owner,
      nickName,
      avatarUrl,
      processingDuration: `${processingDuration}ms`,
      errorMessage: error.message,
      operation: 'createOrUpdateUserProfile'
    });

    return {
      success: false,
      data: null,
      message: error.message || '创建或更新用户资料时发生错误',
      error: error
    };
  }
};

// 导出所有函数
module.exports = {
  readUserByOwner,
  createUser,
  updateUserByOwner,
  addToMyCalendar,
  addToCollectedCalendar,
  removeFromCollectedCalendar,
  readUsersByOwnerList,
  createOrUpdateUserProfile,
  uploadAvatarToCloud
};
