// pages/calendarDetail/calendarDetail.js
// 引入数据库操作工具
const calendarDataDB = require('../../utils/db-calendar-data.js');
const userScheduleDB = require('../../utils/db-user-schedule.js');
const calendarDB = require('../../utils/db-calendar.js');
const userAuth = require('../../utils/user-auth.js');
const userDB = require('../../utils/db-user.js');
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    calendarData: {},
    calendarInfo: {},
    loading: true,
    bookingStatus: {
      isBooked: false,
      isInQueue: false,
      queuePosition: 0,
      isCapacityFull: false,
      loading: false,
      isClosed: false // 时间段是否关闭预约
    },
    currentUserOpenId: '',
    defaultAvatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
    bookingUsers: [],
    queueUsers: [],
    currentCalendarId: '',
    showUserProfileModal: false,
    tempUserProfile: {
      avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      nickName: ''
    },
    // 收藏相关数据
    isCollected: false,
    currentUserOwner: '',
    collectionLoading: false,

    // 权限相关数据
    isOwner: false,

    // 关闭预约相关数据
    bookingControlLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('CalendarDetail page loaded with options:', options)

    // 记录页面加载日志
    realtimeLog.logPageLoad('calendarDetail', options);
    realtimeLog.addFilterMsg('calendar-detail');

    // 记录关键参数
    if (options.calendar_id) {
      realtimeLog.addFilterMsg(`calendar-${options.calendar_id}`);
    }
    if (options.from_share === 'true') {
      realtimeLog.addFilterMsg('share-access');
    }

    // 记录加载开始时间，用于确保最小加载时间
    this.loadingStartTime = Date.now();

    // 启用分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 处理从时间网格传递过来的参数（优先处理）
    if (options.date && options.time) {
      // 必须传递日历ID，否则报错
      if (!options.calendar_id) {
        console.error('CalendarDetail页面缺少必需的calendar_id参数');
        wx.showToast({
          title: '页面参数错误',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 保存日历ID
      this.setData({
        currentCalendarId: options.calendar_id
      });
      console.log('设置当前日历ID:', options.calendar_id);

      const selectedDateTime = {
        date: options.date,
        time: options.time,
        title: `${options.date} ${options.time}`,
        description: `您选择的时间段：${options.date} ${options.time}`,
        items: [
          {
            id: 'selected-time',
            time: options.time,
            title: '选中的时间段',
            description: `日期：${options.date}，时间：${options.time}`,
            location: '',
            priority: 'normal',
            completed: false
          }
        ]
      }

      this.setData({
        calendarData: selectedDateTime
        // 注意：不在这里设置loading: false，等所有数据加载完成后再设置
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: `${options.date} ${options.time}`
      })

      // 初始化所有功能，完成后设置loading为false
      this.initAllFeatures();
    }
    // 处理日历ID访问（包括分享链接和普通访问）
    else if (options.calendar_id) {
      if (options.from_share === 'true') {
        // 分享链接访问，可能包含date和time参数
        this.handleShareAccess(options.calendar_id, options.date, options.time);
      } else {
        // 普通日历访问
        this.handleCalendarAccess(options.calendar_id);
      }
      return;
    }
    // 从页面参数中获取日历数据（保持原有功能）
    else if (options.calendarData) {
      try {
        const calendarData = JSON.parse(decodeURIComponent(options.calendarData))
        this.setData({
          calendarData: calendarData,
          loading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: calendarData.title || '日历详情'
        })
      } catch (error) {
        console.error('解析日历数据失败:', error)
        realtimeLog.logError('解析日历数据失败', error, {
          page: 'calendarDetail',
          operation: 'parseCalendarData',
          options: options
        });
        this.setData({
          loading: false
        })
        wx.showToast({
          title: '数据加载失败',
          icon: 'error'
        })
      }
    } else {
      this.setData({
        loading: false
      })
    }
  },

  // 日程项点击处理
  onItemTap(e) {
    const { itemData } = e.detail
    wx.showToast({
      title: `点击了: ${itemData.title}`,
      icon: 'none'
    })
  },

  // 切换完成状态
  onToggleComplete(e) {
    const { itemData, completed } = e.detail
    // 这里可以更新数据或调用API
    wx.showToast({
      title: completed ? '已完成' : '未完成',
      icon: 'success'
    })
  },

  /**
   * 处理分享链接访问
   */
  async handleShareAccess(calendar_id, date, time) {
    try {
      this.loadingStartTime = Date.now(); // 记录加载开始时间
      this.setData({ loading: true });

      console.log('处理分享链接访问', { calendar_id, date, time });
      realtimeLog.info('[分享访问] 开始处理', {
        calendar_id: calendar_id,
        date: date,
        time: time,
        accessType: 'share'
      });

      // 查询日历信息
      const calendarResult = await calendarDB.readCalendarById(calendar_id);
      realtimeLog.logDbOperation('readCalendarById', 'calendar', { calendar_id }, calendarResult.success, calendarResult);

      if (!calendarResult.success || !calendarResult.data) {
        realtimeLog.error('[分享访问] 日历不存在', { calendar_id: calendar_id, result: calendarResult });
        wx.showToast({
          title: '日历不存在',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      const calendarInfo = calendarResult.data;
      realtimeLog.info('[分享访问] 日历信息加载成功', {
        calendarId: calendarInfo._id,
        calendarName: calendarInfo.name,
        owner: calendarInfo.owner
      });

      // 构建日历数据，包含date和time信息
      const calendarData = {
        _id: calendarInfo._id,
        name: calendarInfo.name,
        title: calendarInfo.name,
        description: calendarInfo.description,
        items: [] // 可以根据需要加载日历事件
      };

      // 如果分享链接包含date和time，添加到calendarData中
      if (date) {
        calendarData.date = date;
      }
      if (time) {
        calendarData.time = time;
      }

      // 设置页面数据
      this.setData({
        calendarData: calendarData,
        calendarInfo: calendarInfo,
        currentCalendarId: calendar_id,
        isOwner: calendarInfo.owner === this.data.currentUserOpenId
        // 注意：不在这里设置loading: false，等收藏状态初始化完成后再设置
      });

      // 设置页面标题，如果有date和time则显示具体时间
      let pageTitle = calendarInfo.name || '日历详情';
      if (date && time) {
        pageTitle = `${date} ${time}`;
      } else if (date) {
        pageTitle = `${calendarInfo.name || '日历'} - ${date}`;
      }

      wx.setNavigationBarTitle({
        title: pageTitle
      });

      // 显示分享访问提示
      const toastMessage = date && time
        ? '已获得预约详情访问权限'
        : '已获得日历访问权限';

      wx.showToast({
        title: toastMessage,
        icon: 'success'
      });

      // 初始化收藏状态，完成后设置loading为false
      await this.initCollectionStatus();

      // 确保骨架屏有足够的显示时间
      await this.ensureMinimumLoadingTime();
      this.setData({ loading: false });

    } catch (error) {
      console.error('处理分享访问失败:', error);
      realtimeLog.logError('分享访问处理失败', error, {
        page: 'calendarDetail',
        calendar_id: calendar_id,
        date: date,
        time: time
      });
      wx.showToast({
        title: '加载日历失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 处理普通日历访问
   */
  async handleCalendarAccess(calendar_id) {
    try {
      this.loadingStartTime = Date.now(); // 记录加载开始时间
      this.setData({ loading: true });

      // 查询日历信息
      const calendarResult = await calendarDB.readCalendarById(calendar_id);
      realtimeLog.logDbOperation('readCalendarById', 'calendar', { calendar_id }, calendarResult.success, calendarResult);

      if (!calendarResult.success || !calendarResult.data) {
        realtimeLog.logError('日历不存在', new Error('日历查询失败'), {
          page: 'calendarDetail',
          calendar_id: calendar_id,
          operation: 'handleCalendarAccess'
        });
        wx.showToast({
          title: '日历不存在',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      const calendarInfo = calendarResult.data;

      // 设置页面数据
      this.setData({
        calendarData: {
          _id: calendarInfo._id,
          name: calendarInfo.name,
          title: calendarInfo.name,
          description: calendarInfo.description,
          items: [] // 可以根据需要加载日历事件
        },
        calendarInfo: calendarInfo,
        currentCalendarId: calendar_id,
        isOwner: calendarInfo.owner === this.data.currentUserOpenId
        // 注意：不在这里设置loading: false，等收藏状态初始化完成后再设置
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: calendarInfo.name || '日历详情'
      });

      // 初始化收藏状态，完成后设置loading为false
      await this.initCollectionStatus();

      // 确保骨架屏有足够的显示时间
      await this.ensureMinimumLoadingTime();
      this.setData({ loading: false });

    } catch (error) {
      console.error('处理日历访问失败:', error);
      realtimeLog.logError('处理日历访问失败', error, {
        page: 'calendarDetail',
        calendar_id: calendar_id,
        operation: 'handleCalendarAccess'
      });
      wx.showToast({
        title: '加载日历失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 初始化收藏状态
   */
  async initCollectionStatus() {
    try {
      // 获取当前用户信息
      const userInfo = await userAuth.getCurrentUser();
      if (!userInfo.success || !userInfo.openId) {
        return;
      }

      this.setData({
        currentUserOwner: userInfo.openId
      });

      // 查询用户信息，检查收藏状态
      const userResult = await userDB.readUserByOwner(userInfo.openId);
      realtimeLog.logDbOperation('readUserByOwner', 'user', { userId: userInfo.openId }, userResult.success, userResult);

      if (userResult.success && userResult.data) {
        const collectedCalendars = userResult.data.collected_calendar || [];
        const calendar_id = this.data.currentCalendarId || this.data.calendarInfo._id;
        const isCollected = collectedCalendars.includes(calendar_id);

        this.setData({
          isCollected: isCollected
        });
      }
    } catch (error) {
      console.error('初始化收藏状态失败:', error);
      realtimeLog.logError('初始化收藏状态失败', error, {
        page: 'calendarDetail',
        operation: 'initCollectionStatus'
      });
    }
  },

  /**
   * 切换收藏状态
   */
  async onToggleCollection() {
    const { currentUserOwner, isCollected, currentCalendarId, calendarInfo, collectionLoading } = this.data;

    if (collectionLoading) {
      return;
    }

    if (!currentUserOwner) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const calendar_id = currentCalendarId || calendarInfo._id;
    if (!calendar_id) {
      wx.showToast({
        title: '日历信息错误',
        icon: 'none'
      });
      return;
    }

    this.setData({ collectionLoading: true });

    try {
      let result;
      if (isCollected) {
        // 取消收藏
        result = await userDB.removeFromCollectedCalendar(currentUserOwner, calendar_id);
        realtimeLog.logDbOperation('removeFromCollectedCalendar', 'user', { userId: currentUserOwner, calendarId: calendar_id }, result.success, result);
      } else {
        // 添加收藏
        result = await userDB.addToCollectedCalendar(currentUserOwner, calendar_id);
        realtimeLog.logDbOperation('addToCollectedCalendar', 'user', { userId: currentUserOwner, calendarId: calendar_id }, result.success, result);
      }

      if (result.success) {
        this.setData({
          isCollected: !isCollected
        });

        wx.showToast({
          title: isCollected ? '取消收藏成功' : '收藏成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error);
      realtimeLog.logError('切换收藏状态失败', error, {
        page: 'calendarDetail',
        operation: 'onToggleCollection',
        calendarId: calendar_id,
        action: isCollected ? 'uncollect' : 'collect'
      });
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ collectionLoading: false });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新检查预约状态，确保状态是最新的
    // 这样可以处理从其他页面返回时状态可能已经改变的情况
    if (this.data.calendarData.date && this.data.calendarData.time && this.data.currentUserOpenId) {
      this.checkBookingStatus();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 确保最小加载时间
   */
  async ensureMinimumLoadingTime() {
    if (!this.loadingStartTime) {
      this.loadingStartTime = Date.now();
    }

    const minLoadingTime = 600; // 最小加载时间600ms
    const elapsedTime = Date.now() - this.loadingStartTime;
    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

    if (remainingTime > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingTime));
    }
  },

  /**
   * 初始化所有功能
   */
  async initAllFeatures() {
    const startTime = Date.now();
    const minLoadingTime = 800; // 最小加载时间800ms，确保骨架屏有足够的显示时间

    try {
      // 并行执行所有初始化操作以提高性能
      await Promise.all([
        this.initBookingFeature(),
        this.loadCalendarInfo(),
        this.loadBookingUsers(),
        this.initCollectionStatus()
      ]);

      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      // 如果加载太快，等待剩余时间以确保良好的用户体验
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      // 所有数据加载完成，设置loading为false
      this.setData({
        loading: false
      });

      console.log('页面初始化完成，总耗时:', Date.now() - startTime, 'ms');
    } catch (error) {
      console.error('初始化功能失败:', error);
      realtimeLog.logError('初始化功能失败', error, {
        page: 'calendarDetail',
        operation: 'initAllFeatures'
      });

      // 即使出错也要等待最小时间，然后设置loading为false
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      this.setData({
        loading: false
      });
    }
  },

  /**
   * 初始化预约功能
   */
  async initBookingFeature() {
    try {
      // 获取当前用户信息
      const userInfo = await userAuth.getCurrentUser();
      if (userInfo.success && userInfo.openId) {
        this.setData({
          currentUserOpenId: userInfo.openId
        });

        // 检查是否有日历ID
        if (!this.data.currentCalendarId) {
          console.error('初始化预约功能失败：缺少日历ID');
          wx.showToast({
            title: '日历ID缺失',
            icon: 'error'
          });
          return;
        }

        // 检查当前时间段的预约状态
        await this.checkBookingStatus();
      }
    } catch (error) {
      console.error('初始化预约功能失败:', error);
      realtimeLog.logError('初始化预约功能失败', error, {
        page: 'calendarDetail',
        operation: 'initBookingFeature'
      });
    }
  },



  /**
   * 检查预约状态
   */
  async checkBookingStatus() {
    const { calendarData, currentUserOpenId, currentCalendarId } = this.data;

    if (!calendarData.date || !calendarData.time || !currentUserOpenId) {
      return;
    }

    if (!currentCalendarId) {
      console.error('CalendarDetail页面缺少日历ID，无法检查预约状态');
      wx.showToast({
        title: '日历ID缺失',
        icon: 'error'
      });
      return;
    }

    try {
      // 解析日期 - 添加更强的保护
      const dateObj = new Date(calendarData.date);

      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        const error = new Error(`无效的日期格式: ${calendarData.date}`);
        realtimeLog.logError('日期格式无效', error, {
          page: 'calendarDetail',
          operation: 'checkBookingStatus',
          date: calendarData.date
        });
        throw error;
      }

      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 调试信息：检查参数
      console.log('checkBookingStatus 参数检查:', {
        currentCalendarId,
        year,
        month,
        day,
        calendarDataDate: calendarData.date,
        dateObj: dateObj.toString(),
        isValidDate: !isNaN(dateObj.getTime())
      });

      // 验证参数有效性
      if (!currentCalendarId || !year || !month || !day || isNaN(year) || isNaN(month) || isNaN(day)) {
        const error = new Error(`参数无效: calendarId=${currentCalendarId}, year=${year}, month=${month}, day=${day}`);
        realtimeLog.logError('预约状态检查参数无效', error, {
          page: 'calendarDetail',
          operation: 'checkBookingStatus',
          currentCalendarId,
          year,
          month,
          day
        });
        throw error;
      }

      // 查询预约数据（包含关闭状态信息）
      const result = await calendarDataDB.getBookingDataByDate(currentCalendarId, year, month, day);
      realtimeLog.logDbOperation('getBookingDataByDate', 'calendarData', { calendarId: currentCalendarId, year, month, day }, result.success, result);

      // 检查预约关闭状态
      let isClosed = false;
      if (result.success && result.closedBookings) {
        const closedBookings = result.closedBookings;
        const isAllDayClosed = closedBookings.allDay === true;
        const isTimeSlotClosed = closedBookings.timeSlots && closedBookings.timeSlots.includes(calendarData.time);
        isClosed = isAllDayClosed || isTimeSlotClosed;
      }

      if (result.success && result.bookings && result.bookings[calendarData.time]) {
        const booking = result.bookings[calendarData.time];
        const bookedUsers = booking.bookedUsers || [];
        const waitingQueue = booking.waitingQueue || [];
        const maxCapacity = booking.maxCapacity || 5;

        const isBooked = bookedUsers.includes(currentUserOpenId);
        const queueItem = waitingQueue.find(item => item.userOpenId === currentUserOpenId);
        const isInQueue = !!queueItem;
        const isCapacityFull = bookedUsers.length >= maxCapacity;

        this.setData({
          'bookingStatus.isBooked': isBooked,
          'bookingStatus.isInQueue': isInQueue,
          'bookingStatus.queuePosition': queueItem ? queueItem.queuePosition : 0,
          'bookingStatus.isCapacityFull': isCapacityFull,
          'bookingStatus.isClosed': isClosed
        });
      } else {
        this.setData({
          'bookingStatus.isBooked': false,
          'bookingStatus.isInQueue': false,
          'bookingStatus.queuePosition': 0,
          'bookingStatus.isCapacityFull': false,
          'bookingStatus.isClosed': isClosed
        });
      }
    } catch (error) {
      console.error('检查预约状态失败:', error);
      realtimeLog.logError('检查预约状态失败', error, {
        page: 'calendarDetail',
        operation: 'checkBookingStatus',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });
    }
  },

  /**
   * 验证预约参数
   */
  validateBookingParams() {
    const { calendarData, currentUserOpenId } = this.data;

    if (!currentUserOpenId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return false;
    }

    if (!calendarData.date) {
      wx.showToast({
        title: '日期信息缺失',
        icon: 'none'
      });
      return false;
    }

    if (!calendarData.time) {
      wx.showToast({
        title: '时间信息缺失',
        icon: 'none'
      });
      return false;
    }

    // 验证日期格式
    const dateObj = new Date(calendarData.date);
    if (isNaN(dateObj.getTime())) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'none'
      });
      return false;
    }

    // 验证时间格式 (HH:mm)
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(calendarData.time)) {
      wx.showToast({
        title: '时间格式错误',
        icon: 'none'
      });
      return false;
    }

    // 验证预约时间不能是过去的时间
    const bookingDateTime = new Date(`${calendarData.date} ${calendarData.time}`);
    const now = new Date();
    if (bookingDateTime <= now) {
      wx.showToast({
        title: '不能预约过去的时间',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  /**
   * 预约时间段
   */
  async onBookTimeSlot() {
    // 验证参数
    if (!this.validateBookingParams()) {
      return;
    }

    const { calendarData, currentUserOpenId } = this.data;

    // 显示确认对话框
    const confirmResult = await this.showConfirmDialog(
      '确认预约',
      `确定要预约 ${calendarData.date} ${calendarData.time} 吗？`
    );

    if (!confirmResult) {
      return;
    }

    this.setData({
      'bookingStatus.loading': true
    });

    try {
      // 获取用户信息并保存到User表
      const userProfile = await this.getUserProfileWithComponents();

      // 解析日期
      const dateObj = new Date(calendarData.date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 检查日历ID
      const { currentCalendarId } = this.data;
      if (!currentCalendarId) {
        const error = new Error('日历ID缺失，无法执行预约操作');
        realtimeLog.logError('预约操作-日历ID缺失', error, {
          page: 'calendarDetail',
          operation: 'onBookTimeSlot'
        });
        throw error;
      }
      console.log('使用日历ID:', currentCalendarId);

      // 执行预约到CalendarData表
      const calendarResult = await calendarDataDB.bookTimeSlot(
        currentCalendarId,
        year,
        month,
        day,
        calendarData.time,
        currentUserOpenId,
        5 // 默认容量为5
      );
      realtimeLog.logDbOperation('bookTimeSlot', 'calendarData', {
        calendarId: currentCalendarId,
        year,
        month,
        day,
        time: calendarData.time,
        userId: currentUserOpenId
      }, calendarResult.success, calendarResult);

      if (calendarResult.success) {
        if (calendarResult.code === 'BOOKING_SUCCESS') {
          // 直接预约成功，创建UserSchedule记录
          const userScheduleResult = await userScheduleDB.createUserScheduleFromDateTime(
            currentUserOpenId,
            currentCalendarId, // 使用当前日历ID
            calendarData.date,
            calendarData.time
          );
          realtimeLog.logDbOperation('createUserScheduleFromDateTime', 'userSchedule', {
            userId: currentUserOpenId,
            calendarId: currentCalendarId,
            date: calendarData.date,
            time: calendarData.time
          }, userScheduleResult.success, userScheduleResult);

          if (userScheduleResult.success) {
            wx.showToast({
              title: '预约成功',
              icon: 'success'
            });

            this.setData({
              'bookingStatus.isBooked': true
            });
          } else {
            // UserSchedule创建失败，但CalendarData已成功，记录警告但不影响用户体验
            console.warn('UserSchedule记录创建失败:', userScheduleResult.message);

            wx.showToast({
              title: '预约成功',
              icon: 'success'
            });

            this.setData({
              'bookingStatus.isBooked': true
            });
          }
        } else if (calendarResult.code === 'ADDED_TO_QUEUE') {
          // 加入排队成功
          wx.showToast({
            title: calendarResult.message,
            icon: 'success'
          });

          this.setData({
            'bookingStatus.isInQueue': true,
            'bookingStatus.queuePosition': calendarResult.data.queueCount
          });
        }

        // 重新加载预约用户列表
        await this.loadBookingUsers();

        // 触发页面刷新事件
        this.triggerPageRefresh();
      } else {
        wx.showToast({
          title: calendarResult.message || '操作失败',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('预约失败:', error);
      realtimeLog.logError('预约操作失败', error, {
        page: 'calendarDetail',
        calendarId: this.data.currentCalendarId,
        date: calendarData.date,
        time: calendarData.time
      });

      let errorMessage = '预约失败，请重试';
      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络';
        } else if (error.message.includes('权限')) {
          errorMessage = '没有预约权限';
        } else if (error.message.includes('满员')) {
          errorMessage = '该时间段已满员';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({
        'bookingStatus.loading': false
      });
    }
  },

  /**
   * 取消预约
   */
  async onCancelBooking() {
    const { calendarData, currentUserOpenId } = this.data;

    // 基本参数验证
    if (!currentUserOpenId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    if (!calendarData.date || !calendarData.time) {
      wx.showToast({
        title: '预约信息缺失',
        icon: 'none'
      });
      return;
    }

    // 显示确认对话框
    const confirmResult = await this.showConfirmDialog(
      '确认取消',
      `确定要取消 ${calendarData.date} ${calendarData.time} 的预约吗？`
    );

    if (!confirmResult) {
      return;
    }

    this.setData({
      'bookingStatus.loading': true
    });

    try {
      // 解析日期
      const dateObj = new Date(calendarData.date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 检查日历ID
      const { currentCalendarId } = this.data;
      if (!currentCalendarId) {
        const error = new Error('日历ID缺失，无法执行取消预约操作');
        realtimeLog.logError('取消预约-日历ID缺失', error, {
          page: 'calendarDetail',
          operation: 'onCancelBooking'
        });
        throw error;
      }
      console.log('取消预约使用日历ID:', currentCalendarId);

      // 先从UserSchedule表查找并删除对应记录
      const scheduledTime = new Date(`${calendarData.date} ${calendarData.time}:00`).getTime();
      const userSchedules = await userScheduleDB.readUserSchedulesByOwner(currentUserOpenId);
      realtimeLog.logDbOperation('readUserSchedulesByOwner', 'userSchedule', { userId: currentUserOpenId }, userSchedules.success, userSchedules);

      if (userSchedules.success && userSchedules.data.length > 0) {
        // 查找匹配的预约记录
        const matchingSchedule = userSchedules.data.find(schedule =>
          schedule.calendar_id === currentCalendarId &&
          schedule.scheduled_time === scheduledTime
        );

        if (matchingSchedule) {
          const deleteResult = await userScheduleDB.deleteUserSchedule(matchingSchedule._id);
          realtimeLog.logDbOperation('deleteUserSchedule', 'userSchedule', { scheduleId: matchingSchedule._id }, deleteResult.success, deleteResult);
          if (!deleteResult.success) {
            console.warn('删除UserSchedule记录失败:', deleteResult.message);
          }
        }
      }

      // 执行取消预约（从CalendarData表）
      const calendarResult = await calendarDataDB.cancelBooking(
        currentCalendarId,
        year,
        month,
        day,
        calendarData.time,
        currentUserOpenId
      );
      realtimeLog.logDbOperation('cancelBooking', 'calendarData', {
        calendarId: currentCalendarId,
        year,
        month,
        day,
        time: calendarData.time,
        userId: currentUserOpenId
      }, calendarResult.success, calendarResult);

      if (calendarResult.success) {
        wx.showToast({
          title: '取消预约成功',
          icon: 'success'
        });

        this.setData({
          'bookingStatus.isBooked': false
        });

        // 重新加载预约用户列表
        await this.loadBookingUsers();

        // 触发页面刷新事件
        this.triggerPageRefresh();
      } else {
        wx.showToast({
          title: calendarResult.message || '取消预约失败',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('取消预约失败:', error);
      realtimeLog.logError('取消预约失败', error, {
        page: 'calendarDetail',
        operation: 'onCancelBooking',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });

      let errorMessage = '取消预约失败，请重试';
      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络';
        } else if (error.message.includes('权限')) {
          errorMessage = '没有取消权限';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({
        'bookingStatus.loading': false
      });
    }
  },

  /**
   * 加入排队
   */
  async onJoinQueue() {
    // 验证参数
    if (!this.validateBookingParams()) {
      return;
    }

    const { calendarData, currentUserOpenId } = this.data;

    // 显示确认对话框
    const confirmResult = await this.showConfirmDialog(
      '加入排队',
      `确定要加入 ${calendarData.date} ${calendarData.time} 的排队吗？`
    );

    if (!confirmResult) {
      return;
    }

    this.setData({
      'bookingStatus.loading': true
    });

    try {
      // 获取用户信息并保存到User表
      const userProfile = await this.getUserProfileWithComponents();

      // 解析日期
      const dateObj = new Date(calendarData.date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 检查日历ID
      const { currentCalendarId } = this.data;
      if (!currentCalendarId) {
        const error = new Error('日历ID缺失，无法执行加入排队操作');
        realtimeLog.logError('加入排队-日历ID缺失', error, {
          page: 'calendarDetail',
          operation: 'onJoinQueue'
        });
        throw error;
      }
      console.log('加入排队使用日历ID:', currentCalendarId);

      // 执行预约操作（会自动加入排队）
      const calendarResult = await calendarDataDB.bookTimeSlot(
        currentCalendarId,
        year,
        month,
        day,
        calendarData.time,
        currentUserOpenId,
        5 // 默认最大容量
      );
      realtimeLog.logDbOperation('bookTimeSlot-queue', 'calendarData', {
        calendarId: currentCalendarId,
        year,
        month,
        day,
        time: calendarData.time,
        userId: currentUserOpenId,
        operation: 'joinQueue'
      }, calendarResult.success, calendarResult);

      if (calendarResult.success) {
        if (calendarResult.code === 'ADDED_TO_QUEUE') {
          wx.showToast({
            title: '加入排队成功',
            icon: 'success'
          });

          this.setData({
            'bookingStatus.isInQueue': true,
            'bookingStatus.queuePosition': calendarResult.data.queueCount
          });

          // 重新加载预约和排队用户列表
          await this.loadBookingUsers();

          // 触发页面刷新事件
          this.triggerPageRefresh();
        } else {
          wx.showToast({
            title: calendarResult.message || '操作失败',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: calendarResult.message || '加入排队失败',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('加入排队失败:', error);
      realtimeLog.logError('加入排队失败', error, {
        page: 'calendarDetail',
        operation: 'onJoinQueue',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });
      wx.showToast({
        title: '加入排队失败，请重试',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({
        'bookingStatus.loading': false
      });
    }
  },

  /**
   * 取消排队
   */
  async onCancelQueue() {
    const { calendarData, currentUserOpenId } = this.data;

    // 基本参数验证
    if (!currentUserOpenId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    if (!calendarData.date || !calendarData.time) {
      wx.showToast({
        title: '排队信息缺失',
        icon: 'none'
      });
      return;
    }

    // 显示确认对话框
    const confirmResult = await this.showConfirmDialog(
      '确认取消排队',
      `确定要取消 ${calendarData.date} ${calendarData.time} 的排队吗？`
    );

    if (!confirmResult) {
      return;
    }

    this.setData({
      'bookingStatus.loading': true
    });

    try {
      // 解析日期
      const dateObj = new Date(calendarData.date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 检查日历ID
      const { currentCalendarId } = this.data;
      if (!currentCalendarId) {
        const error = new Error('日历ID缺失，无法执行取消排队操作');
        realtimeLog.logError('取消排队-日历ID缺失', error, {
          page: 'calendarDetail',
          operation: 'onCancelQueue'
        });
        throw error;
      }
      console.log('取消排队使用日历ID:', currentCalendarId);

      // 执行取消排队操作
      const cancelResult = await calendarDataDB.cancelQueue(
        currentCalendarId,
        year,
        month,
        day,
        calendarData.time,
        currentUserOpenId
      );
      realtimeLog.logDbOperation('cancelQueue', 'calendarData', {
        calendarId: currentCalendarId,
        year,
        month,
        day,
        time: calendarData.time,
        userId: currentUserOpenId
      }, cancelResult.success, cancelResult);

      if (cancelResult.success) {
        wx.showToast({
          title: '取消排队成功',
          icon: 'success'
        });

        this.setData({
          'bookingStatus.isInQueue': false,
          'bookingStatus.queuePosition': 0
        });

        // 重新加载预约和排队用户列表
        await this.loadBookingUsers();

        // 触发页面刷新事件
        this.triggerPageRefresh();
      } else {
        wx.showToast({
          title: cancelResult.message || '取消排队失败',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('取消排队失败:', error);
      realtimeLog.logError('取消排队失败', error, {
        page: 'calendarDetail',
        operation: 'onCancelQueue',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });
      wx.showToast({
        title: '取消排队失败，请重试',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({
        'bookingStatus.loading': false
      });
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        content: content,
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 触发页面刷新事件
   */
  triggerPageRefresh() {
    // 通知其他页面刷新数据
    const pages = getCurrentPages();
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
      if (prevPage && typeof prevPage.onBookingStatusChanged === 'function') {
        prevPage.onBookingStatusChanged();
      }
    }
  },

  /**
   * 获取用户信息并保存到User表
   */
  async getUserInfoAndSave() {
    try {
      const { currentUserOpenId } = this.data;

      if (!currentUserOpenId) {
        console.warn('用户未登录，无法获取用户信息');
        return;
      }

      // 获取微信用户信息
      const userProfile = await this.getWechatUserProfile();

      // 保存用户信息到User表
      const userProfileResult = await userDB.createOrUpdateUserProfile(
        currentUserOpenId,
        userProfile.nickName,
        userProfile.avatarUrl
      );
      realtimeLog.logDbOperation('createOrUpdateUserProfile', 'user', {
        userId: currentUserOpenId,
        nickName: userProfile.nickName
      }, userProfileResult.success, userProfileResult);

      if (userProfileResult.success) {
        console.log('用户信息保存成功');
      } else {
        console.warn('保存用户信息失败:', userProfileResult.message);
      }
    } catch (error) {
      console.error('获取并保存用户信息失败:', error);
      realtimeLog.logError('获取并保存用户信息失败', error, {
        page: 'calendarDetail',
        operation: 'getUserInfoAndSave'
      });
    }
  },

  /**
   * 使用头像昵称填写组件获取用户信息
   */
  async getUserProfileWithComponents() {
    return new Promise((resolve) => {
      // 先尝试从本地存储获取用户信息
      const storedProfile = wx.getStorageSync('userProfile');
      if (storedProfile && storedProfile.nickName) {
        console.log('使用本地存储的用户信息');

        // 保存到数据库
        userDB.createOrUpdateUserProfile(
          this.data.currentUserOpenId,
          storedProfile.nickName,
          storedProfile.avatarUrl
        ).then(() => {
          resolve(storedProfile);
        }).catch(() => {
          resolve(storedProfile);
        });
        return;
      }

      // 显示用户信息填写弹窗
      this.setData({
        showUserProfileModal: true,
        tempUserProfile: {
          avatarUrl: this.data.defaultAvatarUrl,
          nickName: ''
        }
      });

      // 设置回调函数
      this.userProfileResolve = resolve;
    });
  },

  /**
   * 弹窗中选择头像
   */
  onModalChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    this.setData({
      'tempUserProfile.avatarUrl': avatarUrl
    });
  },

  /**
   * 弹窗中输入昵称
   */
  onModalNicknameInput(e) {
    const nickName = e.detail.value;
    this.setData({
      'tempUserProfile.nickName': nickName
    });
  },

  /**
   * 确认用户信息
   */
  async onUserProfileConfirm() {
    const { tempUserProfile, currentUserOpenId } = this.data;

    if (!tempUserProfile.nickName || tempUserProfile.nickName.trim() === '') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    try {
      // 保存到数据库
      const saveResult = await userDB.createOrUpdateUserProfile(
        currentUserOpenId,
        tempUserProfile.nickName,
        tempUserProfile.avatarUrl
      );
      realtimeLog.logDbOperation('createOrUpdateUserProfile-confirm', 'user', {
        userId: currentUserOpenId,
        nickName: tempUserProfile.nickName
      }, saveResult.success, saveResult);

      // 保存到本地存储
      wx.setStorageSync('userProfile', {
        nickName: tempUserProfile.nickName,
        avatarUrl: tempUserProfile.avatarUrl,
        updateTime: Date.now()
      });

      // 关闭弹窗
      this.setData({
        showUserProfileModal: false
      });

      // 返回用户信息
      if (this.userProfileResolve) {
        this.userProfileResolve(tempUserProfile);
        this.userProfileResolve = null;
      }

    } catch (error) {
      console.error('保存用户信息失败:', error);
      realtimeLog.logError('保存用户信息失败', error, {
        page: 'calendarDetail',
        operation: 'onUserProfileConfirm'
      });
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 取消用户信息填写
   */
  onUserProfileCancel() {
    // 关闭弹窗
    this.setData({
      showUserProfileModal: false
    });

    // 使用默认信息
    const defaultProfile = {
      nickName: '微信用户',
      avatarUrl: this.data.defaultAvatarUrl
    };

    // 保存默认信息到数据库
    userDB.createOrUpdateUserProfile(
      this.data.currentUserOpenId,
      defaultProfile.nickName,
      defaultProfile.avatarUrl
    ).catch((error) => {
      console.error('保存默认用户信息失败:', error);
    });

    // 返回默认信息
    if (this.userProfileResolve) {
      this.userProfileResolve(defaultProfile);
      this.userProfileResolve = null;
    }
  },

  /**
   * 加载日历信息
   */
  async loadCalendarInfo() {
    try {
      const { currentCalendarId } = this.data;

      if (!currentCalendarId) {
        console.error('加载日历信息失败：缺少日历ID');
        wx.showToast({
          title: '日历ID缺失',
          icon: 'error'
        });
        return;
      }

      const result = await calendarDB.readCalendarById(currentCalendarId);
      realtimeLog.logDbOperation('readCalendarById', 'calendar', { currentCalendarId }, result.success, result);

      if (result.success && result.data) {
        this.setData({
          calendarInfo: result.data,
          isOwner: result.data.owner === this.data.currentUserOpenId
        });
      }
    } catch (error) {
      console.error('加载日历信息失败:', error);
      realtimeLog.logError('加载日历信息失败', error, {
        page: 'calendarDetail',
        operation: 'loadCalendarInfo',
        calendarId: currentCalendarId
      });
    }
  },

  /**
   * 加载预约用户列表
   */
  async loadBookingUsers() {
    try {
      const { calendarData, currentCalendarId } = this.data;

      if (!calendarData.date || !calendarData.time || !currentCalendarId) {
        return;
      }

      // 解析日期 - 添加更强的保护
      const dateObj = new Date(calendarData.date);

      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        const error = new Error(`无效的日期格式: ${calendarData.date}`);
        realtimeLog.logError('加载预约用户-日期格式无效', error, {
          page: 'calendarDetail',
          operation: 'loadBookingUsers',
          date: calendarData.date
        });
        throw error;
      }

      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 调试信息：检查参数
      console.log('loadBookingUsers 参数检查:', {
        currentCalendarId,
        year,
        month,
        day,
        calendarDataDate: calendarData.date,
        dateObj: dateObj.toString(),
        isValidDate: !isNaN(dateObj.getTime())
      });

      // 验证参数有效性
      if (!currentCalendarId || !year || !month || !day || isNaN(year) || isNaN(month) || isNaN(day)) {
        const error = new Error(`参数无效: calendarId=${currentCalendarId}, year=${year}, month=${month}, day=${day}`);
        realtimeLog.logError('加载预约用户-参数无效', error, {
          page: 'calendarDetail',
          operation: 'loadBookingUsers',
          currentCalendarId,
          year,
          month,
          day
        });
        throw error;
      }

      // 获取预约数据
      const bookingResult = await calendarDataDB.getBookingDataByDate(currentCalendarId, year, month, day);
      realtimeLog.logDbOperation('getBookingDataByDate', 'calendarData', { calendarId: currentCalendarId, year, month, day }, bookingResult.success, bookingResult);

      if (bookingResult.success && bookingResult.bookings && bookingResult.bookings[calendarData.time]) {
        const timeSlotBooking = bookingResult.bookings[calendarData.time];
        const bookedUsers = timeSlotBooking.bookedUsers || [];
        const waitingQueue = timeSlotBooking.waitingQueue || [];
        const maxCapacity = timeSlotBooking.maxCapacity || 5;

        // 处理已预约用户
        let bookingUsersData = [];
        if (bookedUsers.length > 0) {
          const usersResult = await userDB.readUsersByOwnerList(bookedUsers);
          realtimeLog.logDbOperation('readUsersByOwnerList-booked', 'user', { userIds: bookedUsers }, usersResult.success, usersResult);
          if (usersResult.success) {
            bookingUsersData = usersResult.data;
          }
        }

        // 处理排队用户
        let queueUsersData = [];
        if (waitingQueue.length > 0) {
          const queueUserIds = waitingQueue.map(item => item.userOpenId);
          const queueUsersResult = await userDB.readUsersByOwnerList(queueUserIds);
          realtimeLog.logDbOperation('readUsersByOwnerList-queue', 'user', { userIds: queueUserIds }, queueUsersResult.success, queueUsersResult);

          if (queueUsersResult.success) {
            // 合并用户信息和排队信息
            queueUsersData = waitingQueue.map(queueItem => {
              const userInfo = queueUsersResult.data.find(user => user.owner === queueItem.userOpenId);
              return {
                ...queueItem,
                ...userInfo,
                queueTimeFormatted: this.formatQueueTime(queueItem.queueTime)
              };
            });
          }
        }

        // 检查当前用户的状态
        const { currentUserOpenId } = this.data;
        const isBooked = bookedUsers.includes(currentUserOpenId);
        const queueItem = waitingQueue.find(item => item.userOpenId === currentUserOpenId);
        const isInQueue = !!queueItem;
        const isCapacityFull = bookedUsers.length >= maxCapacity;

        this.setData({
          bookingUsers: bookingUsersData,
          queueUsers: queueUsersData,
          'bookingStatus.isBooked': isBooked,
          'bookingStatus.isInQueue': isInQueue,
          'bookingStatus.queuePosition': queueItem ? queueItem.queuePosition : 0,
          'bookingStatus.isCapacityFull': isCapacityFull
        });
      } else {
        this.setData({
          bookingUsers: [],
          queueUsers: [],
          'bookingStatus.isBooked': false,
          'bookingStatus.isInQueue': false,
          'bookingStatus.queuePosition': 0,
          'bookingStatus.isCapacityFull': false
        });
      }
    } catch (error) {
      console.error('加载预约用户列表失败:', error);
      realtimeLog.logError('加载预约用户列表失败', error, {
        page: 'calendarDetail',
        operation: 'loadBookingUsers',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });
      this.setData({
        bookingUsers: [],
        queueUsers: []
      });
    }
  },

  /**
   * 格式化排队时间
   */
  formatQueueTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 显示排队状态详情
   */
  onShowQueueStatus() {
    const { bookingStatus, queueUsers } = this.data;

    if (bookingStatus.isInQueue) {
      const queueCount = queueUsers.length;
      const position = bookingStatus.queuePosition;
      const estimatedWaitTime = position * 5; // 假设每人平均等待5分钟

      wx.showModal({
        title: '排队状态',
        content: `您当前排队位置：第${position}位\n前面还有${position - 1}人\n预计等待时间：${estimatedWaitTime}分钟\n\n当有人取消预约时，系统会自动为您安排预约。`,
        showCancel: false,
        confirmText: '知道了'
      });
    } else {
      wx.showModal({
        title: '排队信息',
        content: `当前排队人数：${queueUsers.length}人\n您可以加入排队等待预约机会。`,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },





  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(object) {
    const { calendarData, calendarInfo, currentCalendarId } = this.data;

    console.log('分享事件触发', {
      from: object?.from,
      currentCalendarId,
      hasDate: !!(calendarData && calendarData.date),
      hasTime: !!(calendarData && calendarData.time)
    });

    // 记录分享操作日志
    realtimeLog.logShare('calendarDetail', {
      from: object?.from,
      currentCalendarId: currentCalendarId,
      hasDate: !!(calendarData && calendarData.date),
      hasTime: !!(calendarData && calendarData.time),
      calendarName: (calendarInfo && calendarInfo.name) || (calendarData && calendarData.name)
    });

    // 获取日历ID - 优先级：currentCalendarId > calendarData._id > calendarData.id
    const calendar_id = currentCalendarId ||
                       (calendarData && calendarData._id) ||
                       (calendarData && calendarData.id);

    // 获取日期时间信息
    const date = calendarData && calendarData.date;
    const time = calendarData && calendarData.time;

    // 构建分享路径参数
    const queryParams = [];

    // 必须包含calendar_id，否则下游无法正确处理
    if (calendar_id) {
      queryParams.push(`calendar_id=${encodeURIComponent(calendar_id)}`);
    }

    // 如果有日期时间信息，也要包含
    if (date) {
      queryParams.push(`date=${encodeURIComponent(date)}`);
    }
    if (time) {
      queryParams.push(`time=${encodeURIComponent(time)}`);
    }

    // 标记为分享访问
    queryParams.push('from_share=true');

    // 构建完整的分享路径
    const sharePath = `/pages/calendarDetail/calendarDetail?${queryParams.join('&')}`;

    // 构建分享标题
    let shareTitle;
    if (date && time) {
      // 预约详情分享
      shareTitle = `预约详情：${date} ${time}`;
    } else if (calendar_id) {
      // 日历分享
      const calendarName = (calendarInfo && calendarInfo.name) ||
                          (calendarData && calendarData.name) ||
                          '日历';
      shareTitle = `${calendarName} - 日历分享`;
    } else {
      // 默认分享
      shareTitle = 'BuukMe - 预约管理';
    }

    console.log('分享配置生成', {
      title: shareTitle,
      path: sharePath,
      params: { calendar_id, date, time, from_share: 'true' }
    });

    return {
      title: shareTitle,
      path: sharePath
    };
  },

  /**
   * 关闭时间段预约
   */
  async onCloseTimeSlotBooking() {
    const { calendarData, currentCalendarId, isOwner } = this.data;

    // 权限检查
    if (!isOwner) {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }

    // 确认对话框
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '关闭预约确认',
        content: `确定要关闭 ${calendarData.time} 时段的预约吗？已有预约不会受影响，但新用户将无法预约。`,
        confirmText: '确定关闭',
        cancelText: '取消',
        success: (res) => resolve(res.confirm)
      });
    });

    if (!result) return;

    try {
      this.setData({ bookingControlLoading: true });

      // 解析日期
      const dateObj = new Date(calendarData.date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 导入数据库操作模块
      const calendarDataDB = require('../../utils/db-calendar-data.js');

      // 执行关闭操作
      const closeResult = await calendarDataDB.closeBookingForTimeSlot(
        currentCalendarId,
        year,
        month,
        day,
        calendarData.time
      );
      realtimeLog.logDbOperation('closeBookingForTimeSlot', 'calendarData', {
        calendarId: currentCalendarId,
        year,
        month,
        day,
        time: calendarData.time
      }, closeResult.success, closeResult);

      if (closeResult.success) {
        this.setData({
          bookingControlLoading: false
        });

        wx.showToast({
          title: '已关闭时段预约',
          icon: 'success'
        });

        // 刷新预约状态
        await this.checkBookingStatus();
      } else {
        const error = new Error(closeResult.message);
        realtimeLog.logError('关闭时段预约-操作失败', error, {
          page: 'calendarDetail',
          operation: 'onCloseTimeSlotBooking',
          result: closeResult
        });
        throw error;
      }

    } catch (error) {
      console.error('关闭时段预约失败:', error);
      realtimeLog.logError('关闭时段预约失败', error, {
        page: 'calendarDetail',
        operation: 'onCloseTimeSlotBooking',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });
      this.setData({ bookingControlLoading: false });

      wx.showToast({
        title: error.message || '关闭失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开放时间段预约
   */
  async onOpenTimeSlotBooking() {
    const { calendarData, currentCalendarId, isOwner } = this.data;

    // 权限检查
    if (!isOwner) {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }

    // 确认对话框
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '开放预约确认',
        content: `确定要开放 ${calendarData.time} 时段的预约吗？用户将可以重新预约该时间段。`,
        confirmText: '确定开放',
        cancelText: '取消',
        success: (res) => resolve(res.confirm)
      });
    });

    if (!result) return;

    try {
      this.setData({ bookingControlLoading: true });

      // 解析日期
      const dateObj = new Date(calendarData.date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();

      // 导入数据库操作模块
      const calendarDataDB = require('../../utils/db-calendar-data.js');

      // 执行开放操作
      const openResult = await calendarDataDB.openBookingForTimeSlot(
        currentCalendarId,
        year,
        month,
        day,
        calendarData.time
      );
      realtimeLog.logDbOperation('openBookingForTimeSlot', 'calendarData', {
        calendarId: currentCalendarId,
        year,
        month,
        day,
        time: calendarData.time
      }, openResult.success, openResult);

      if (openResult.success) {
        this.setData({
          bookingControlLoading: false
        });

        wx.showToast({
          title: '已开放时段预约',
          icon: 'success'
        });

        // 刷新预约状态
        await this.checkBookingStatus();
      } else {
        const error = new Error(openResult.message);
        realtimeLog.logError('开放时段预约-操作失败', error, {
          page: 'calendarDetail',
          operation: 'onOpenTimeSlotBooking',
          result: openResult
        });
        throw error;
      }

    } catch (error) {
      console.error('开放时段预约失败:', error);
      realtimeLog.logError('开放时段预约失败', error, {
        page: 'calendarDetail',
        operation: 'onOpenTimeSlotBooking',
        calendarId: this.data.currentCalendarId,
        date: this.data.calendarData?.date,
        time: this.data.calendarData?.time
      });
      this.setData({ bookingControlLoading: false });

      wx.showToast({
        title: error.message || '开放失败',
        icon: 'none'
      });
    }
  }

})
