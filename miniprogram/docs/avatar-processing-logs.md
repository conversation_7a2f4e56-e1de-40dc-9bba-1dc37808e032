# 头像处理流程实时日志说明

## 概述

为了更好地理解头像上传和处理流程，我们在关键步骤添加了详细的实时日志记录。这些日志帮助开发者和运维人员：

1. **追踪处理流程**：了解头像从选择到保存的完整过程
2. **监控性能**：记录处理时间，识别性能瓶颈
3. **错误诊断**：详细记录错误信息，便于问题排查
4. **状态监控**：实时了解头像处理的最终状态

## 日志记录点

### 1. 用户选择头像 (`onModalChooseAvatar`)

**日志类型**: `logInfo`
**操作标识**: `onModalChooseAvatar`

```javascript
realtimeLog.logInfo('用户选择头像', {
  avatarUrl: 'wxfile://tmp_123456.jpg',
  isTemporary: true,
  page: 'calendarDetail',
  operation: 'onModalChooseAvatar'
});
```

**记录信息**:
- 选择的头像URL
- 是否为临时头像
- 页面来源
- 操作类型

### 2. 头像上传到云存储 (`uploadAvatarToCloud`)

#### 2.1 开始上传
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('头像上传开始', {
  tempFilePath: 'wxfile://tmp_123456.jpg',
  owner: 'user_openid_123',
  operation: 'uploadAvatarToCloud'
});
```

#### 2.2 文件名生成
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('头像文件名生成', {
  fileName: 'avatars/user_openid_123_1640995200000_abc123.jpg',
  owner: 'user_openid_123',
  timestamp: 1640995200000,
  randomStr: 'abc123',
  operation: 'uploadAvatarToCloud'
});
```

#### 2.3 上传成功
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('头像上传成功', {
  fileID: 'cloud://env.bucket/avatars/user_openid_123_1640995200000_abc123.jpg',
  cloudPath: 'avatars/user_openid_123_1640995200000_abc123.jpg',
  owner: 'user_openid_123',
  uploadDuration: '1250ms',
  originalPath: 'wxfile://tmp_123456.jpg',
  operation: 'uploadAvatarToCloud'
});
```

#### 2.4 上传失败
**日志类型**: `logError`
```javascript
realtimeLog.logError('头像上传异常', error, {
  tempFilePath: 'wxfile://tmp_123456.jpg',
  owner: 'user_openid_123',
  uploadDuration: '3000ms',
  errorMessage: '网络连接超时',
  operation: 'uploadAvatarToCloud'
});
```

### 3. 用户资料处理 (`createOrUpdateUserProfile`)

#### 3.1 处理开始
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('用户资料处理开始', {
  owner: 'user_openid_123',
  nickName: '张三',
  avatarUrl: 'wxfile://tmp_123456.jpg',
  operation: 'createOrUpdateUserProfile'
});
```

#### 3.2 临时头像检测
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('检测到临时头像', {
  originalAvatarUrl: 'wxfile://tmp_123456.jpg',
  owner: 'user_openid_123',
  operation: 'createOrUpdateUserProfile'
});
```

#### 3.3 头像处理成功
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('临时头像处理成功', {
  originalUrl: 'wxfile://tmp_123456.jpg',
  finalUrl: 'cloud://env.bucket/avatars/user_openid_123_1640995200000_abc123.jpg',
  cloudPath: 'avatars/user_openid_123_1640995200000_abc123.jpg',
  owner: 'user_openid_123',
  operation: 'createOrUpdateUserProfile'
});
```

#### 3.4 数据库操作
**日志类型**: `logDbOperation`
```javascript
realtimeLog.logDbOperation('createUserProfile', 'user', {
  owner: 'user_openid_123',
  userData: { /* 用户数据 */ },
  avatarProcessing: { /* 头像处理结果 */ },
  processingDuration: '2500ms'
}, true, result);
```

### 4. 前端确认保存 (`onUserProfileConfirm`)

#### 4.1 确认开始
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('用户确认信息开始', {
  nickName: '张三',
  avatarUrl: 'wxfile://tmp_123456.jpg',
  isTemporaryAvatar: true,
  userId: 'user_openid_123',
  page: 'calendarDetail',
  operation: 'onUserProfileConfirm'
});
```

#### 4.2 保存成功
**日志类型**: `logInfo`
```javascript
realtimeLog.logInfo('用户信息保存成功', {
  nickName: '张三',
  originalAvatarUrl: 'wxfile://tmp_123456.jpg',
  finalAvatarUrl: 'cloud://env.bucket/avatars/user_openid_123_1640995200000_abc123.jpg',
  avatarProcessed: true,
  userId: 'user_openid_123',
  processingDuration: '3200ms',
  page: 'calendarDetail',
  operation: 'onUserProfileConfirm'
});
```

## 日志查看方法

### 1. 微信开发者工具
在微信开发者工具的控制台中查看实时日志输出。

### 2. 云开发控制台
登录微信云开发控制台，在"运维中心" > "实时日志"中查看详细日志。

### 3. 日志筛选
使用以下关键词筛选相关日志：
- `头像上传`
- `用户资料处理`
- `uploadAvatarToCloud`
- `createOrUpdateUserProfile`
- `onUserProfileConfirm`

## 性能监控指标

### 1. 处理时间
- **头像上传时间**: 通常在 1-3 秒
- **用户资料处理时间**: 通常在 2-5 秒
- **总体确认时间**: 通常在 3-8 秒

### 2. 成功率监控
- **头像上传成功率**: 目标 > 95%
- **用户资料保存成功率**: 目标 > 98%

### 3. 错误类型统计
- 网络错误
- 参数错误
- 云存储错误
- 数据库错误

## 故障排查指南

### 1. 头像上传失败
查看日志关键词：`头像上传失败`、`头像上传异常`
常见原因：
- 网络连接问题
- 云存储权限问题
- 文件格式不支持

### 2. 用户资料保存失败
查看日志关键词：`用户资料处理失败`、`保存用户信息失败`
常见原因：
- 数据库连接问题
- 参数验证失败
- 权限不足

### 3. 头像URL不更新
查看日志关键词：`本地存储头像URL已更新`
检查点：
- 是否检测到临时头像
- 头像上传是否成功
- 本地存储是否更新

## 日志配置

确保在 `utils/realtime-log.js` 中正确配置了日志级别和输出方式，以便在生产环境中也能获得必要的日志信息。
