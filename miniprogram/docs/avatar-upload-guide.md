# 头像上传功能使用指南

## 功能概述

本功能实现了将用户选择的临时头像自动上传到微信云存储，并更新用户数据库中的头像URL。解决了临时头像URL失效的问题。

## 主要特性

1. **自动检测临时头像**：自动识别 `wxfile://` 和 `http://tmp/` 开头的临时头像路径
2. **云存储上传**：将临时头像上传到微信云存储，生成永久可访问的URL
3. **数据库更新**：自动更新用户数据库中的头像URL
4. **本地缓存同步**：更新本地存储中的用户信息
5. **错误处理**：上传失败时使用默认头像

## 技术实现

### 1. 头像上传函数 (`uploadAvatarToCloud`)

```javascript
const uploadAvatarToCloud = async (tempFilePath, owner) => {
  // 生成唯一文件名：avatars/{owner}_{timestamp}_{random}.jpg
  const fileName = `avatars/${owner}_${timestamp}_${randomStr}.jpg`;
  
  // 上传到云存储
  const uploadResult = await wx.cloud.uploadFile({
    cloudPath: fileName,
    filePath: tempFilePath
  });
  
  return uploadResult.fileID; // 返回云存储文件ID
};
```

### 2. 用户资料更新函数 (`createOrUpdateUserProfile`)

```javascript
const createOrUpdateUserProfile = async (owner, nickName, avatarUrl) => {
  let finalAvatarUrl = avatarUrl;
  
  // 检测临时头像路径
  if (avatarUrl && (avatarUrl.startsWith('http://tmp/') || 
                   avatarUrl.includes('wxfile://') || 
                   avatarUrl.startsWith('wxfile://'))) {
    
    // 上传到云存储
    const uploadResult = await uploadAvatarToCloud(avatarUrl, owner);
    if (uploadResult.success) {
      finalAvatarUrl = uploadResult.fileID;
    } else {
      // 使用默认头像
      finalAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
    }
  }
  
  // 更新数据库...
};
```

## 使用方法

### 1. 在页面中使用头像选择组件

```xml
<!-- WXML -->
<button class="avatar-btn" open-type="chooseAvatar" bind:chooseavatar="onModalChooseAvatar">
  <image class="modal-avatar" src="{{tempUserProfile.avatarUrl}}" mode="aspectFill"></image>
</button>
```

### 2. 处理头像选择事件

```javascript
// JavaScript
onModalChooseAvatar(e) {
  const { avatarUrl } = e.detail;
  this.setData({
    'tempUserProfile.avatarUrl': avatarUrl
  });
},
```

### 3. 保存用户信息

```javascript
async onUserProfileConfirm() {
  const { tempUserProfile, currentUserOpenId } = this.data;
  
  // 显示加载提示
  wx.showLoading({ title: '保存中...', mask: true });
  
  // 保存到数据库（自动处理头像上传）
  const saveResult = await userDB.createOrUpdateUserProfile(
    currentUserOpenId,
    tempUserProfile.nickName,
    tempUserProfile.avatarUrl // 可能是临时路径
  );
  
  if (saveResult.success) {
    // 使用返回的最终头像URL
    const finalAvatarUrl = saveResult.avatarUrl;
    
    // 更新本地存储
    wx.setStorageSync('userProfile', {
      nickName: tempUserProfile.nickName,
      avatarUrl: finalAvatarUrl,
      updateTime: Date.now()
    });
    
    wx.showToast({ title: '保存成功', icon: 'success' });
  }
  
  wx.hideLoading();
}
```

## 文件存储结构

云存储中的头像文件按以下结构组织：

```
cloud://your-env.your-bucket/
└── avatars/
    ├── user_123_1640995200000_abc123.jpg
    ├── user_456_1640995300000_def456.jpg
    └── ...
```

文件命名规则：`{用户ID}_{时间戳}_{随机字符串}.jpg`

## 错误处理

1. **上传失败**：自动使用默认头像URL
2. **参数错误**：返回错误信息，不影响其他用户信息保存
3. **网络问题**：显示友好的错误提示

## 测试

运行测试文件验证功能：

```bash
cd miniprogram
node test/avatar-upload-test.js
```

测试覆盖：
- 临时头像路径检测
- 云存储上传功能
- 用户资料创建和更新
- 错误处理机制

## 注意事项

1. **云存储权限**：确保云存储已正确配置读写权限
2. **文件大小**：微信头像选择器返回的图片通常已经过压缩
3. **存储成本**：定期清理不再使用的头像文件
4. **缓存策略**：本地存储会自动更新为最新的头像URL

## 相关文件

- `utils/db-user.js` - 用户数据库操作和头像上传
- `pages/calendarDetail/calendarDetail.js` - 头像选择和保存逻辑
- `test/avatar-upload-test.js` - 功能测试文件
